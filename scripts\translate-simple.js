#!/usr/bin/env node

/**
 * 简化版国际化翻译脚本
 * 专门用于处理当前项目的 zh-CN.js 文件
 */

const fs = require('fs');
const path = require('path');

// 核心翻译映射表
const coreTranslations = {
  // 基础操作
  '确定': 'Confirm',
  '取消': 'Cancel',
  '保存': 'Save',
  '删除': 'Delete',
  '编辑': 'Edit',
  '修改': 'Modify',
  '添加': 'Add',
  '新建': 'Create',
  '创建': 'Create',
  '查询': 'Query',
  '搜索': 'Search',
  '重置': 'Reset',
  '提交': 'Submit',
  '导入': 'Import',
  '导出': 'Export',
  '上传': 'Upload',
  '下载': 'Download',
  '复制': 'Copy',
  '关闭': 'Close',
  '打开': 'Open',
  '启用': 'Enable',
  '停用': 'Disable',
  '是': 'Yes',
  '否': 'No',
  '全部': 'All',
  '选择': 'Select',
  '操作': 'Operation',
  '状态': 'Status',
  '名称': 'Name',
  '类型': 'Type',
  '时间': 'Time',
  '详情': 'Details',
  '信息': 'Information',
  '配置': 'Configuration',
  '管理': 'Management',
  '监控': 'Monitoring',
  '测试': 'Test',
  '执行': 'Execute',
  '成功': 'Success',
  '失败': 'Failed',
  '错误': 'Error',
  '异常': 'Exception',
  '加载中': 'Loading',
  '连接': 'Connect',
  '在线': 'Online',
  '离线': 'Offline',
  
  // 业务术语
  '证券': 'Securities',
  '期货': 'Futures',
  '期权': 'Options',
  '交易': 'Trading',
  '委托': 'Order',
  '成交': 'Trade',
  '报盘': 'Order Routing',
  '风控': 'Risk Control',
  '行情': 'Market Data',
  '核心': 'Core',
  '前置': 'Front-end',
  '网关': 'Gateway',
  '客户端': 'Client',
  '服务器': 'Server',
  '节点': 'Node',
  '集群': 'Cluster',
  '实例': 'Instance',
  '应用': 'Application',
  '系统': 'System',
  '服务': 'Service',
  '功能': 'Function',
  '数据': 'Data',
  '文件': 'File',
  '日志': 'Log',
  '时延': 'Latency',
  '性能': 'Performance',
  '链路': 'Link',
  '地址': 'Address',
  '端口': 'Port',
  '版本': 'Version',
  '账号': 'Account',
  '用户': 'User',
  '列表': 'List',
  '报表': 'Report',
  '统计': 'Statistics',
  '分析': 'Analysis',
  '指标': 'Metric',
  '范围': 'Range',
  '分片': 'Shard',
  '同步': 'Synchronization',
  '队列': 'Queue',
  '内存': 'Memory',
  '线程': 'Thread',
  '任务': 'Task',
  '部署': 'Deployment',
  
  // 交易所
  '上海证券交易所': 'Shanghai Stock Exchange',
  '深圳证券交易所': 'Shenzhen Stock Exchange',
  '上交所': 'SSE',
  '深交所': 'SZSE',
  
  // 时间
  '全天': 'All Day',
  '上午': 'Morning',
  '下午': 'Afternoon',
  '秒': 'seconds',
  '分钟': 'minutes',
  '小时': 'hours',
  '最大': 'Maximum',
  '最小': 'Minimum',
  '平均': 'Average',
  '中位数': 'Median',
  
  // 常用短语
  '请输入': 'Please enter',
  '请选择': 'Please select',
  '操作成功': 'Operation successful',
  '操作失败': 'Operation failed',
  '创建成功': 'Created successfully',
  '创建失败': 'Creation failed',
  '连接成功': 'Connected successfully',
  '连接失败': 'Connection failed',
  '网络异常': 'Network exception',
  '服务异常': 'Service exception',
  '网络请求超时': 'Network request timeout',
  '暂无数据': 'No data available',
  '不能为空': 'Cannot be empty',
  '格式不正确': 'Incorrect format',
  '上传成功': 'Upload successful',
  '上传失败': 'Upload failed',
  '复制成功': 'Copy successful'
};

// 翻译规则
const translationRules = [
  { pattern: /(.+)时延$/, replacement: '$1 Latency' },
  { pattern: /(.+)管理$/, replacement: '$1 Management' },
  { pattern: /(.+)配置$/, replacement: '$1 Configuration' },
  { pattern: /(.+)监控$/, replacement: '$1 Monitoring' },
  { pattern: /(.+)状态$/, replacement: '$1 Status' },
  { pattern: /(.+)信息$/, replacement: '$1 Information' },
  { pattern: /(.+)列表$/, replacement: '$1 List' },
  { pattern: /请输入(.+)$/, replacement: 'Please enter $1' },
  { pattern: /请选择(.+)$/, replacement: 'Please select $1' },
  { pattern: /(.+)成功!?$/, replacement: '$1 successful' },
  { pattern: /(.+)失败!?$/, replacement: '$1 failed' },
  { pattern: /(.+)地址$/, replacement: '$1 Address' },
  { pattern: /(.+)端口$/, replacement: '$1 Port' },
  { pattern: /(.+)节点$/, replacement: '$1 Node' },
  { pattern: /(.+)文件$/, replacement: '$1 File' },
  { pattern: /最近(.+)$/, replacement: 'Last $1' }
];

/**
 * 翻译单个文本
 */
function translateText(text) {
  if (!text || typeof text !== 'string') {
    return text;
  }

  // 直接映射
  if (coreTranslations[text]) {
    return coreTranslations[text];
  }

  // 应用规则（先处理复杂规则）
  for (const rule of translationRules) {
    if (rule.pattern.test(text)) {
      let result = text.replace(rule.pattern, rule.replacement);

      // 递归翻译捕获的部分
      result = result.replace(/\$1/g, (match) => {
        const captured = text.match(rule.pattern)[1];
        const translated = translateText(captured);
        return translated !== captured ? translated : captured;
      });

      return result;
    }
  }

  // 智能分词翻译（按长度排序，优先匹配长词汇）
  const sortedTranslations = Object.entries(coreTranslations)
    .sort(([a], [b]) => b.length - a.length);

  let result = text;
  for (const [chinese, english] of sortedTranslations) {
    if (result.includes(chinese)) {
      // 使用词边界匹配，避免部分匹配
      const regex = new RegExp(chinese.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
      result = result.replace(regex, english);
    }
  }

  return result;
}

/**
 * 递归翻译对象
 */
function translateObject(obj) {
  if (typeof obj === 'string') {
    return translateText(obj);
  }
  
  if (Array.isArray(obj)) {
    return obj.map(translateObject);
  }
  
  if (obj && typeof obj === 'object') {
    const result = {};
    for (const [key, value] of Object.entries(obj)) {
      result[key] = translateObject(value);
    }
    return result;
  }
  
  return obj;
}

/**
 * 解析 ES6 模块文件
 */
function parseESModule(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');

  // 提取 export default 后的对象
  const match = content.match(/export\s+default\s+(\{[\s\S]*?\});?\s*$/);
  if (!match) {
    throw new Error('无法找到 export default 对象');
  }

  let objStr = match[1];

  // 移除尾随分号
  objStr = objStr.replace(/;$/, '');

  try {
    // 使用 Function 构造函数安全地解析对象
    return new Function('return ' + objStr)();
  } catch (error) {
    throw new Error('解析对象失败: ' + error.message);
  }
}

/**
 * 处理文件内容
 */
function processFile(inputPath, outputPath) {
  console.log(`读取文件: ${inputPath}`);

  // 解析源文件对象
  const zhObject = parseESModule(inputPath);

  console.log('开始翻译...');
  const translatedObject = translateObject(zhObject);

  // 生成新文件内容
  const newContent = `export default ${JSON.stringify(translatedObject, null, 4).replace(/"/g, "'")};\n`;

  // 写入文件
  console.log(`写入文件: ${outputPath}`);
  fs.writeFileSync(outputPath, newContent, 'utf8');

  // 统计
  const totalKeys = countKeys(translatedObject);
  console.log(`翻译完成！总共处理了 ${totalKeys} 个键值对`);
}

/**
 * 计算键数量
 */
function countKeys(obj) {
  if (typeof obj !== 'object' || obj === null) {
    return 0;
  }
  
  let count = 0;
  for (const value of Object.values(obj)) {
    if (typeof value === 'string') {
      count++;
    } else if (typeof value === 'object') {
      count += countKeys(value);
    }
  }
  return count;
}

/**
 * 主函数
 */
function main() {
  const projectRoot = path.resolve(__dirname, '..');
  const zhCNPath = path.join(projectRoot, 'src/locales/i18n/zh-CN.js');
  const enUSPath = path.join(projectRoot, 'src/locales/i18n/en-US.js');
  
  try {
    // 检查文件是否存在
    if (!fs.existsSync(zhCNPath)) {
      throw new Error(`源文件不存在: ${zhCNPath}`);
    }
    
    // 备份现有的英文文件
    if (fs.existsSync(enUSPath)) {
      const backupPath = enUSPath + '.backup.' + Date.now();
      fs.copyFileSync(enUSPath, backupPath);
      console.log(`已备份现有文件到: ${backupPath}`);
    }
    
    // 处理翻译
    processFile(zhCNPath, enUSPath);
    
    console.log('✅ 翻译脚本执行完成！');
    
  } catch (error) {
    console.error('❌ 翻译过程中出现错误:', error.message);
    process.exit(1);
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { translateText, translateObject };
