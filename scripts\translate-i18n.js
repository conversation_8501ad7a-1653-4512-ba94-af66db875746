#!/usr/bin/env node

/**
 * 国际化翻译脚本
 * 用于将 zh-CN.js 中的中文内容翻译为英文并更新到 en-US.js
 */

const fs = require('fs');
const path = require('path');

// 翻译映射表 - 常用词汇和短语的翻译
const translationMap = {
  // 基础词汇
  '确定': 'Confirm',
  '取消': 'Cancel',
  '保存': 'Save',
  '删除': 'Delete',
  '编辑': 'Edit',
  '修改': 'Modify',
  '添加': 'Add',
  '新建': 'Create',
  '创建': 'Create',
  '查询': 'Query',
  '搜索': 'Search',
  '重置': 'Reset',
  '提交': 'Submit',
  '导入': 'Import',
  '导出': 'Export',
  '上传': 'Upload',
  '下载': 'Download',
  '复制': 'Copy',
  '关闭': 'Close',
  '打开': 'Open',
  '启用': 'Enable',
  '停用': 'Disable',
  '开启': 'Enable',
  '关闭': 'Close',
  '是': 'Yes',
  '否': 'No',
  '全部': 'All',
  '选择': 'Select',
  '请选择': 'Please Select',
  '操作': 'Operation',
  '状态': 'Status',
  '名称': 'Name',
  '类型': 'Type',
  '时间': 'Time',
  '日期': 'Date',
  '详情': 'Details',
  '信息': 'Information',
  '配置': 'Configuration',
  '管理': 'Management',
  '监控': 'Monitoring',
  '测试': 'Test',
  '执行': 'Execute',
  '运行': 'Run',
  '停止': 'Stop',
  '暂停': 'Pause',
  '继续': 'Continue',
  '完成': 'Complete',
  '成功': 'Success',
  '失败': 'Failed',
  '错误': 'Error',
  '异常': 'Exception',
  '警告': 'Warning',
  '提示': 'Tip',
  '加载中': 'Loading',
  '处理中': 'Processing',
  '连接': 'Connect',
  '断开': 'Disconnect',
  '在线': 'Online',
  '离线': 'Offline',
  '正常': 'Normal',
  '异常': 'Abnormal',
  
  // 业务相关词汇
  '证券': 'Securities',
  '期货': 'Futures',
  '期权': 'Options',
  '交易': 'Trading',
  '委托': 'Order',
  '成交': 'Trade',
  '报盘': 'Order Routing',
  '风控': 'Risk Control',
  '行情': 'Market Data',
  '核心': 'Core',
  '前置': 'Front-end',
  '网关': 'Gateway',
  '客户端': 'Client',
  '服务器': 'Server',
  '节点': 'Node',
  '集群': 'Cluster',
  '实例': 'Instance',
  '应用': 'Application',
  '系统': 'System',
  '服务': 'Service',
  '功能': 'Function',
  '模块': 'Module',
  '组件': 'Component',
  '接口': 'Interface',
  '数据': 'Data',
  '文件': 'File',
  '日志': 'Log',
  '监视器': 'Monitor',
  '时延': 'Latency',
  '延迟': 'Delay',
  '性能': 'Performance',
  '吞吐': 'Throughput',
  '链路': 'Link',
  '路径': 'Path',
  '地址': 'Address',
  '端口': 'Port',
  '协议': 'Protocol',
  '版本': 'Version',
  '账号': 'Account',
  '用户': 'User',
  '权限': 'Permission',
  '角色': 'Role',
  '组': 'Group',
  '列表': 'List',
  '表格': 'Table',
  '报表': 'Report',
  '统计': 'Statistics',
  '分析': 'Analysis',
  '趋势': 'Trend',
  '图表': 'Chart',
  '指标': 'Metric',
  '度量': 'Measurement',
  '单位': 'Unit',
  '范围': 'Range',
  '区间': 'Interval',
  '分片': 'Shard',
  '分区': 'Partition',
  '备份': 'Backup',
  '恢复': 'Recovery',
  '同步': 'Synchronization',
  '异步': 'Asynchronous',
  '队列': 'Queue',
  '缓存': 'Cache',
  '内存': 'Memory',
  '存储': 'Storage',
  '索引': 'Index',
  '事务': 'Transaction',
  '会话': 'Session',
  '连接': 'Connection',
  '线程': 'Thread',
  '进程': 'Process',
  '任务': 'Task',
  '作业': 'Job',
  '调度': 'Schedule',
  '部署': 'Deployment',
  '发布': 'Release',
  '环境': 'Environment',
  '开发': 'Development',
  '测试': 'Testing',
  '生产': 'Production',
  
  // 交易所相关
  '上海证券交易所': 'Shanghai Stock Exchange',
  '深圳证券交易所': 'Shenzhen Stock Exchange',
  '上交所': 'SSE',
  '深交所': 'SZSE',
  '郑州商品交易所': 'Zhengzhou Commodity Exchange',
  '大连商品交易所': 'Dalian Commodity Exchange',
  '上海期货交易所': 'Shanghai Futures Exchange',
  '中国金融期货交易所': 'China Financial Futures Exchange',
  
  // 时间相关
  '全天': 'All Day',
  '上午': 'Morning',
  '下午': 'Afternoon',
  '早盘': 'Morning Session',
  '午盘': 'Afternoon Session',
  '日盘': 'Day Session',
  '夜盘': 'Night Session',
  '盘前': 'Pre-market',
  '盘后': 'After-market',
  '开盘': 'Market Open',
  '收盘': 'Market Close',
  '集合竞价': 'Call Auction',
  '连续竞价': 'Continuous Auction',
  
  // 数量和单位
  '个': 'Count',
  '条': 'Items',
  '次': 'Times',
  '秒': 'Seconds',
  '分钟': 'Minutes',
  '小时': 'Hours',
  '天': 'Days',
  '周': 'Weeks',
  '月': 'Months',
  '年': 'Years',
  '纳秒': 'Nanoseconds',
  '微秒': 'Microseconds',
  '毫秒': 'Milliseconds',
  '最大': 'Maximum',
  '最小': 'Minimum',
  '平均': 'Average',
  '中位数': 'Median',
  '标准差': 'Standard Deviation',
  
  // 状态相关
  '待处理': 'Pending',
  '处理中': 'Processing',
  '已完成': 'Completed',
  '已取消': 'Cancelled',
  '已暂停': 'Paused',
  '运行中': 'Running',
  '已停止': 'Stopped',
  '就绪': 'Ready',
  '空闲': 'Idle',
  '忙碌': 'Busy',
  '活跃': 'Active',
  '非活跃': 'Inactive',
  
  // 常用短语
  '请输入': 'Please enter',
  '请选择': 'Please select',
  '操作成功': 'Operation successful',
  '操作失败': 'Operation failed',
  '创建成功': 'Created successfully',
  '创建失败': 'Creation failed',
  '更新成功': 'Updated successfully',
  '更新失败': 'Update failed',
  '删除成功': 'Deleted successfully',
  '删除失败': 'Deletion failed',
  '保存成功': 'Saved successfully',
  '保存失败': 'Save failed',
  '连接成功': 'Connected successfully',
  '连接失败': 'Connection failed',
  '网络异常': 'Network exception',
  '服务异常': 'Service exception',
  '网络请求超时': 'Network request timeout',
  '暂无数据': 'No data available',
  '加载中': 'Loading',
  '数据加载失败': 'Data loading failed',
  '请稍候': 'Please wait',
  '确认删除': 'Confirm deletion',
  '不能为空': 'Cannot be empty',
  '格式不正确': 'Incorrect format',
  '超出限制': 'Exceeds limit',
  '字符长度': 'Character length',
  '不得超过': 'Must not exceed',
  '至少': 'At least',
  '最多': 'At most',
  '输入': 'Input',
  '输出': 'Output',
  '上传成功': 'Upload successful',
  '上传失败': 'Upload failed',
  '下载成功': 'Download successful',
  '下载失败': 'Download failed',
  '复制成功': 'Copy successful',
  '该浏览器不支持复制': 'This browser does not support copying'
};

// 复杂翻译规则
const complexTranslationRules = [
  // 时延相关
  { pattern: /(.+)时延$/, replacement: '$1 Latency' },
  { pattern: /(.+)延迟$/, replacement: '$1 Delay' },
  { pattern: /(.+)上行时延$/, replacement: '$1 Upstream Latency' },
  { pattern: /(.+)下行时延$/, replacement: '$1 Downstream Latency' },
  { pattern: /(.+)总时延$/, replacement: '$1 Total Latency' },
  { pattern: /(.+)处理时延$/, replacement: '$1 Processing Latency' },
  
  // 管理相关
  { pattern: /(.+)管理$/, replacement: '$1 Management' },
  { pattern: /(.+)配置$/, replacement: '$1 Configuration' },
  { pattern: /(.+)监控$/, replacement: '$1 Monitoring' },
  { pattern: /(.+)统计$/, replacement: '$1 Statistics' },
  { pattern: /(.+)分析$/, replacement: '$1 Analysis' },
  
  // 数量相关
  { pattern: /(.+)个数$/, replacement: '$1 Count' },
  { pattern: /(.+)数量$/, replacement: '$1 Quantity' },
  { pattern: /(.+)次数$/, replacement: '$1 Times' },
  
  // 状态相关
  { pattern: /(.+)状态$/, replacement: '$1 Status' },
  { pattern: /(.+)信息$/, replacement: '$1 Information' },
  { pattern: /(.+)详情$/, replacement: '$1 Details' },
  { pattern: /(.+)列表$/, replacement: '$1 List' },
  
  // 请输入相关
  { pattern: /请输入(.+)$/, replacement: 'Please enter $1' },
  { pattern: /请选择(.+)$/, replacement: 'Please select $1' },
  
  // 成功失败相关
  { pattern: /(.+)成功!?$/, replacement: '$1 successful' },
  { pattern: /(.+)失败!?$/, replacement: '$1 failed' },
  
  // 地址端口相关
  { pattern: /(.+)地址$/, replacement: '$1 Address' },
  { pattern: /(.+)端口$/, replacement: '$1 Port' },
  { pattern: /(.+)IP$/, replacement: '$1 IP' },
  
  // 节点相关
  { pattern: /(.+)节点$/, replacement: '$1 Node' },
  { pattern: /(.+)集群$/, replacement: '$1 Cluster' },
  { pattern: /(.+)实例$/, replacement: '$1 Instance' },
  
  // 文件相关
  { pattern: /(.+)文件$/, replacement: '$1 File' },
  { pattern: /(.+)目录$/, replacement: '$1 Directory' },
  { pattern: /(.+)路径$/, replacement: '$1 Path' },
  
  // 时间相关
  { pattern: /(.+)时间$/, replacement: '$1 Time' },
  { pattern: /(.+)日期$/, replacement: '$1 Date' },
  { pattern: /最近(.+)$/, replacement: 'Last $1' },
  
  // 百分位相关
  { pattern: /(\d+)分位$/, replacement: '$1th Percentile' },
  { pattern: /(\d+)分位数$/, replacement: '$1th Percentile' },
  
  // 单位相关
  { pattern: /(.+)\((.+)\)$/, replacement: '$1 ($2)' }
];

/**
 * 翻译单个文本
 * @param {string} text 要翻译的中文文本
 * @returns {string} 翻译后的英文文本
 */
function translateText(text) {
  if (!text || typeof text !== 'string') {
    return text;
  }

  // 首先检查直接映射
  if (translationMap[text]) {
    return translationMap[text];
  }

  // 应用复杂翻译规则
  for (const rule of complexTranslationRules) {
    if (rule.pattern.test(text)) {
      let result = text.replace(rule.pattern, rule.replacement);
      
      // 递归翻译替换后的部分
      result = result.replace(/\$1/g, (match, offset, string) => {
        const captured = text.match(rule.pattern)[1];
        return translateText(captured) || captured;
      });
      
      return result;
    }
  }

  // 如果没有找到翻译，尝试分词翻译
  let result = text;
  for (const [chinese, english] of Object.entries(translationMap)) {
    if (result.includes(chinese)) {
      result = result.replace(new RegExp(chinese, 'g'), english);
    }
  }

  return result;
}

/**
 * 递归翻译对象
 * @param {any} obj 要翻译的对象
 * @returns {any} 翻译后的对象
 */
function translateObject(obj) {
  if (typeof obj === 'string') {
    return translateText(obj);
  }
  
  if (Array.isArray(obj)) {
    return obj.map(translateObject);
  }
  
  if (obj && typeof obj === 'object') {
    const result = {};
    for (const [key, value] of Object.entries(obj)) {
      result[key] = translateObject(value);
    }
    return result;
  }
  
  return obj;
}

/**
 * 安全地解析 JavaScript 对象字符串
 * @param {string} objStr 对象字符串
 * @returns {object} 解析后的对象
 */
function parseJSObject(objStr) {
  // 移除 export default 和分号
  let cleanStr = objStr.replace(/export\s+default\s+/, '').replace(/;?\s*$/, '');

  try {
    // 尝试使用 JSON.parse（需要先转换为有效的 JSON）
    let jsonStr = cleanStr
      .replace(/'/g, '"')  // 单引号转双引号
      .replace(/(\w+):/g, '"$1":')  // 属性名加引号
      .replace(/,\s*}/g, '}')  // 移除尾随逗号
      .replace(/,\s*]/g, ']'); // 移除数组尾随逗号

    return JSON.parse(jsonStr);
  } catch (e) {
    // 如果 JSON.parse 失败，使用 Function 构造函数（相对安全）
    try {
      return new Function('return ' + cleanStr)();
    } catch (e2) {
      throw new Error('无法解析对象字符串: ' + e2.message);
    }
  }
}

/**
 * 主翻译函数
 */
function main() {
  const zhCNPath = path.join(__dirname, '../src/locales/i18n/zh-CN.js');
  const enUSPath = path.join(__dirname, '../src/locales/i18n/en-US.js');

  try {
    console.log('开始翻译国际化文件...');

    // 读取中文文件
    console.log('读取 zh-CN.js 文件...');
    const zhCNContent = fs.readFileSync(zhCNPath, 'utf8');

    // 解析对象
    console.log('解析中文对象...');
    const zhCNObject = parseJSObject(zhCNContent);

    console.log('开始翻译...');
    const translatedObject = translateObject(zhCNObject);

    // 生成英文文件内容
    const enUSContent = `export default ${JSON.stringify(translatedObject, null, 4).replace(/"/g, "'")};\n`;

    // 写入英文文件
    console.log('写入 en-US.js 文件...');
    fs.writeFileSync(enUSPath, enUSContent, 'utf8');

    console.log('翻译完成！');
    console.log(`已生成英文翻译文件: ${enUSPath}`);

    // 显示统计信息
    const totalKeys = countKeys(translatedObject);
    console.log(`总共翻译了 ${totalKeys} 个键值对`);

  } catch (error) {
    console.error('翻译过程中出现错误:', error.message);
    process.exit(1);
  }
}

/**
 * 递归计算对象中的键数量
 * @param {any} obj 要计算的对象
 * @returns {number} 键的总数
 */
function countKeys(obj) {
  if (typeof obj !== 'object' || obj === null) {
    return 0;
  }

  let count = 0;
  for (const [key, value] of Object.entries(obj)) {
    if (typeof value === 'string') {
      count++;
    } else if (typeof value === 'object') {
      count += countKeys(value);
    }
  }
  return count;
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  translateText,
  translateObject,
  translationMap,
  complexTranslationRules
};
