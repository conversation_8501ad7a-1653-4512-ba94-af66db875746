// 🌍 合并后的国际化使用示例

// 新的结构：
// - common: 通用文案
// - modules: 业务模块文案（原components + pages）
// - utils: 工具函数文案
// - api: API相关文案
// - store: 状态管理文案

export default {
    template: `
        <div>
            <!-- 通用文案 -->
            <h-button>{{ $t('common.query') }}</h-button>
            <h-button>{{ $t('common.add') }}</h-button>
            
            <!-- 模块文案（原来的components和pages都在这里） -->
            <div>{{ $t('modules.ldpDataObservation.dataMonitor') }}</div>
            <div>{{ $t('modules.createRule.ruleManage') }}</div>
            <div>{{ $t('modules.managementQuery.pleaseSelect') }}</div>
            
            <!-- 工具文案 -->
            <span>{{ $t('utils.formatDate') }}</span>
            
            <!-- API文案 -->
            <div>{{ $t('api.networkTimeout') }}</div>
        </div>
    `,
    
    methods: {
        showMessage() {
            // 通用提示
            this.$hMessage.success(this.$t('common.success'));
            
            // 模块特定提示
            this.$hMessage.info(this.$t('modules.ldpTable.noData'));
        }
    }
};

// 路径对比：
// 原来：
// - $t('components.ldpDataObservation.dataMonitor')
// - $t('pages.createRule.ruleManage')
//
// 现在：
// - $t('modules.ldpDataObservation.dataMonitor')
// - $t('modules.createRule.ruleManage')
