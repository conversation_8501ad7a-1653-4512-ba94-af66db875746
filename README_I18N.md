# 🌍 国际化文案使用说明

## 📋 概述

已成功从项目的523个文件中提取了3387条中文文案，生成了完整的国际化键值对文件。

## 📁 文件结构

```
src/locales/i18n/
├── zh-CN.js              # 中文文案 (3387条)
├── en-US.js              # 英文文案 (待翻译)
├── index.js              # 导出文件
├── mapping.json          # 文案映射关系
└── extraction-report.json # 提取报告

scripts/
└── extract-i18n-optimized.js # 提取脚本

文档/
├── FINAL_INTERNATIONALIZATION_REPORT.md # 详细报告
└── QUICK_START_GUIDE.md                  # 快速使用指南
```

## 🚀 快速开始

### 1. 导入国际化文件

```javascript
import customLocales from '@/locales/i18n';
```

### 2. 在Vue组件中使用

```vue
<template>
  <div>
    <!-- 通用操作 -->
    <h-button>{{ $t('common.query') }}</h-button>
    <h-button>{{ $t('common.add') }}</h-button>
    <h-button>{{ $t('common.edit') }}</h-button>
    <h-button>{{ $t('common.delete') }}</h-button>
    
    <!-- 业务模块文案 -->
    <h2>{{ $t('modules.createRule.ruleManage') }}</h2>
    <div>{{ $t('modules.ldpDataObservation.dataMonitor') }}</div>
    <div>{{ $t('modules.managementQuery.pleaseSelect') }}</div>
    
    <!-- 提示信息 -->
    <div v-if="!data.length">{{ $t('common.noData') }}</div>
  </div>
</template>
```

### 3. 在JavaScript中使用

```javascript
export default {
  methods: {
    showMessage() {
      this.$hMessage.success(this.$t('common.success'));
    },
    
    confirmDelete() {
      this.$hMsgBoxSafe.confirm({
        title: this.$t('common.confirm'),
        content: this.$t('common.confirmDelete')
      });
    }
  }
};
```

## 📚 常用文案路径

### 通用操作
- `common.query` - 查询
- `common.add` - 添加
- `common.edit` - 编辑
- `common.delete` - 删除
- `common.save` - 保存
- `common.cancel` - 取消

### 状态提示
- `common.success` - 成功
- `common.failed` - 失败
- `common.loading` - 加载中
- `common.noData` - 暂无数据

### 表单提示
- `common.pleaseSelect` - 请选择
- `common.pleaseInput` - 请输入

## 📊 统计数据

- **总文件数**: 523个
- **总文案数**: 3387条
- **分类分布**:
  - modules: 3193条 (94.3%) - 业务模块（原components + pages合并）
  - common: 148条 (4.4%) - 通用文案
  - utils: 42条 (1.2%) - 工具函数
  - api: 3条 (0.1%) - API相关
  - store: 1条 (0.0%) - 状态管理

## 🔧 维护

### 重新提取文案
```bash
npm run extract-i18n
```

### 查找文案
在 `src/locales/i18n/mapping.json` 中搜索原中文文案找到对应的key。

## 📖 详细文档

- 📄 [完整报告](./FINAL_INTERNATIONALIZATION_REPORT.md)
- 🚀 [快速使用指南](./QUICK_START_GUIDE.md)

---

✨ **开始使用国际化，让您的应用支持多语言！**
