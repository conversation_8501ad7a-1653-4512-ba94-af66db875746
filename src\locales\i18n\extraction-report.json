{"summary": {"totalFiles": 523, "processedFiles": 421, "totalTexts": 3387, "duration": "1秒", "extractTime": "2025-07-31T06:47:24.590Z"}, "categories": {"common": {"totalTexts": 148, "modules": {}}, "api": {"totalTexts": 3, "modules": {}}, "modules": {"totalTexts": 3193, "modules": {"accordObservation": 10, "analyse": 77, "analyseConfig": 56, "appBindingCore": 15, "brokerDataLimit": 60, "businessMonitor": 19, "common": 220, "coreReplayObservation": 114, "dataSecondAppearance": 42, "eccomProduct": 4, "endpointConfig": 90, "latencyTrendAnalysis": 36, "ldpDataObservation": 293, "ldpLinkConfig": 186, "ldpLogCenter": 88, "ldpMonitor": 79, "ldpProduct": 98, "ldpTable": 105, "locateConfig": 36, "managementQuery": 72, "marketAllLink": 40, "mcDataObservation": 151, "mcDeploy": 23, "mdbDataObservation": 46, "mdbPrivilegeManage": 141, "networkSendAndRecevied": 83, "productDataStorage": 71, "productServiceConfig": 108, "productTimeAnalysis": 14, "rcmBacklogMonitor": 11, "rcmDeploy": 187, "rcmObservation": 54, "secondAppearance": 33, "sms": 17, "sqlTable": 111, "transaction": 33, "tripartiteServiceConfig": 18, "ustTableVerification": 78, "accordMonitor": 4, "analyseData": 14, "apmMonitorConfig": 1, "appRunningState": 8, "displaySettingDrawer": 9, "index": 27, "createRule": 33, "addModal": 2, "helpModal": 1, "routeConfig": 9, "routeInfoForm": 6, "util": 8, "clusterMonitor": 1, "ldpAppMonitor": 1, "marketMonitor": 5, "marketNodeDelayList": 12, "marketPenetrateList": 21, "marketTimeDelay": 6, "constant": 10, "tableSelector": 3, "detailDrawer": 3, "exportHistory": 10, "exportTable": 1, "noticeManagerList": 3, "productServiceList": 2, "productTimeDetail": 11, "productTimeSummary": 4, "publishStatusDetail": 7, "smsList": 32, "sqlCores": 13, "threadInfoOverview": 1, "topoMonitor": 4, "tripartiteServiceList": 2}}, "store": {"totalTexts": 1, "modules": {}}, "utils": {"totalTexts": 42, "modules": {}}}, "topFiles": [{"file": "config\\exchangeConfig.js", "textsCount": 115}, {"file": "components\\common\\topo\\ldpNodeTopo.js", "textsCount": 99}, {"file": "components\\coreReplayObservation\\coreReplayDetail.vue", "textsCount": 51}, {"file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue", "textsCount": 50}, {"file": "views\\index\\createRule\\createRule.vue", "textsCount": 49}, {"file": "components\\ldpLinkConfig\\productInfoConfig.vue", "textsCount": 45}, {"file": "components\\mcDataObservation\\mcTopic.vue", "textsCount": 44}, {"file": "components\\ldpDataObservation\\front\\backendStat.vue", "textsCount": 42}, {"file": "views\\index\\smsList.vue", "textsCount": 42}, {"file": "components\\transaction\\settingModal.vue", "textsCount": 41}, {"file": "config\\spanConfig.js", "textsCount": 40}, {"file": "components\\ldpDataObservation\\core\\funNumProcess.vue", "textsCount": 39}, {"file": "views\\index\\latencyTrendAnalysis.vue", "textsCount": 39}, {"file": "components\\ldpProduct\\businessBox\\businessPoptip.js", "textsCount": 38}, {"file": "components\\ldpProduct\\businessBox\\dataAccordBusinessPoptip.js", "textsCount": 37}, {"file": "components\\ustTableVerification\\createVerificationTask.vue", "textsCount": 36}, {"file": "views\\index\\mdbPrivilegeManage.vue", "textsCount": 36}, {"file": "views\\index\\sqlTable.vue", "textsCount": 36}, {"file": "components\\ldpDataObservation\\core\\nodeDeploy.vue", "textsCount": 35}, {"file": "views\\index\\marketPenetrateList.vue", "textsCount": 35}]}