# 国际化翻译脚本

这个脚本用于自动将 `zh-CN.js` 中的中文内容翻译为英文并更新到 `en-US.js` 文件中。

## 功能特性

- 🚀 **自动化翻译**: 批量处理所有中文文本
- 📚 **智能词典**: 内置丰富的业务词汇翻译映射
- 🔧 **规则引擎**: 支持复杂的翻译规则和模式匹配
- 🛡️ **安全解析**: 安全地解析 JavaScript 对象，避免代码注入
- 📊 **统计报告**: 显示翻译进度和统计信息
- 🎯 **精确翻译**: 针对金融交易系统的专业术语优化

## 使用方法

### 1. 直接运行脚本

```bash
node scripts/translate-i18n.js
```

### 2. 通过 npm 脚本运行

在 `package.json` 中添加脚本：

```json
{
  "scripts": {
    "translate": "node scripts/translate-i18n.js",
    "i18n:translate": "node scripts/translate-i18n.js"
  }
}
```

然后运行：

```bash
npm run translate
# 或
npm run i18n:translate
```

## 翻译规则

### 1. 直接映射翻译

脚本内置了丰富的中英文词汇映射表，包括：

- **基础词汇**: 确定→Confirm, 取消→Cancel, 保存→Save 等
- **业务术语**: 证券→Securities, 期货→Futures, 交易→Trading 等
- **技术术语**: 节点→Node, 集群→Cluster, 实例→Instance 等
- **状态词汇**: 成功→Success, 失败→Failed, 运行中→Running 等

### 2. 模式匹配翻译

支持复杂的翻译规则，例如：

- `(.+)时延` → `$1 Latency`
- `(.+)管理` → `$1 Management`
- `请输入(.+)` → `Please enter $1`
- `(.+)成功` → `$1 successful`

### 3. 递归翻译

对于复合词汇，脚本会递归翻译各个组成部分，确保翻译的准确性。

## 支持的业务领域

### 金融交易
- 证券交易所名称
- 交易类型和状态
- 风控和合规术语
- 市场数据相关

### 系统架构
- 分布式系统组件
- 网络和通信
- 数据存储和处理
- 监控和运维

### 用户界面
- 表单和控件
- 消息和提示
- 操作和状态
- 导航和菜单

## 输出示例

运行脚本后，您会看到类似的输出：

```
开始翻译国际化文件...
读取 zh-CN.js 文件...
解析中文对象...
开始翻译...
写入 en-US.js 文件...
翻译完成！
已生成英文翻译文件: /path/to/src/locales/i18n/en-US.js
总共翻译了 2847 个键值对
```

## 自定义翻译

### 添加新的词汇映射

在 `translationMap` 对象中添加新的映射：

```javascript
const translationMap = {
  // 现有映射...
  '您的中文词汇': 'Your English Translation',
  '另一个词汇': 'Another Translation'
};
```

### 添加新的翻译规则

在 `complexTranslationRules` 数组中添加新规则：

```javascript
const complexTranslationRules = [
  // 现有规则...
  { pattern: /您的模式(.+)$/, replacement: 'Your Pattern $1' }
];
```

## 注意事项

1. **备份原文件**: 运行脚本前建议备份原有的 `en-US.js` 文件
2. **检查结果**: 翻译完成后请检查生成的英文文件，确保翻译质量
3. **专业术语**: 对于特定的业务术语，可能需要手动调整翻译
4. **格式保持**: 脚本会保持原有的对象结构和代码格式

## 故障排除

### 常见问题

1. **文件路径错误**
   - 确保脚本在正确的目录下运行
   - 检查 `zh-CN.js` 和 `en-US.js` 文件是否存在

2. **解析错误**
   - 检查 `zh-CN.js` 文件的语法是否正确
   - 确保文件使用 UTF-8 编码

3. **翻译质量问题**
   - 检查翻译映射表是否包含相关词汇
   - 考虑添加新的翻译规则

### 调试模式

可以修改脚本添加调试输出：

```javascript
// 在 translateText 函数中添加
console.log(`翻译: "${text}" -> "${result}"`);
```

## 扩展功能

脚本支持以下扩展：

1. **多语言支持**: 可以扩展支持其他语言（如日语、韩语等）
2. **API 翻译**: 集成在线翻译 API 提高翻译质量
3. **翻译缓存**: 缓存翻译结果提高性能
4. **增量翻译**: 只翻译新增或修改的内容

## 贡献

欢迎提交 Issue 和 Pull Request 来改进翻译脚本：

1. 添加新的词汇映射
2. 优化翻译规则
3. 修复翻译错误
4. 提升脚本性能

## 许可证

本脚本遵循项目的许可证协议。
