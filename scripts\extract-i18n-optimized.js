#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

/**
 * 优化版中文文案提取脚本
 * 修复key生成问题，确保生成有意义的英文key
 */

class OptimizedI18nExtractor {
    constructor() {
        this.srcDir = path.join(__dirname, '../src');
        this.outputDir = path.join(__dirname, '../src/locales/i18n');
        this.extractedTexts = new Map();
        this.allTexts = new Set();
        this.processedFiles = [];
        this.keyCounter = new Map(); // 用于避免重复key

        // 统计信息
        this.stats = {
            totalFiles: 0,
            processedFiles: 0,
            totalTexts: 0,
            startTime: Date.now()
        };

        // 扩展的中文到英文映射
        this.mappings = {
            // 操作类
            查询: 'query',
            搜索: 'search',
            添加: 'add',
            新增: 'add',
            创建: 'create',
            编辑: 'edit',
            修改: 'edit',
            更新: 'update',
            删除: 'delete',
            移除: 'remove',
            保存: 'save',
            取消: 'cancel',
            确认: 'confirm',
            提交: 'submit',
            重置: 'reset',
            刷新: 'refresh',
            导出: 'export',
            导入: 'import',
            上传: 'upload',
            下载: 'download',
            复制: 'copy',
            粘贴: 'paste',
            清空: 'clear',
            选择: 'select',
            全选: 'selectAll',
            展开: 'expand',
            收起: 'collapse',
            显示: 'show',
            隐藏: 'hide',
            启用: 'enable',
            禁用: 'disable',
            开启: 'open',
            关闭: 'close',
            连接: 'connect',
            断开: 'disconnect',
            登录: 'login',
            登出: 'logout',
            注册: 'register',
            注销: 'unregister',

            // 状态类
            成功: 'success',
            失败: 'failed',
            错误: 'error',
            警告: 'warning',
            提示: 'tip',
            信息: 'info',
            加载中: 'loading',
            运行中: 'running',
            已停止: 'stopped',
            已暂停: 'paused',
            已完成: 'finished',
            待处理: 'pending',
            处理中: 'processing',
            正常: 'normal',
            异常: 'abnormal',
            在线: 'online',
            离线: 'offline',
            活跃: 'active',
            非活跃: 'inactive',
            有效: 'valid',
            无效: 'invalid',

            // 数据类
            数据: 'data',
            信息: 'info',
            详情: 'detail',
            列表: 'list',
            表格: 'table',
            记录: 'record',
            日志: 'log',
            报告: 'report',
            统计: 'statistics',
            分析: 'analysis',
            监控: 'monitor',
            观测: 'observation',
            指标: 'metric',
            参数: 'param',
            配置: 'config',
            设置: 'setting',
            选项: 'option',
            属性: 'property',

            // 时间类
            时间: 'time',
            日期: 'date',
            开始: 'start',
            结束: 'end',
            开始时间: 'startTime',
            结束时间: 'endTime',
            创建时间: 'createTime',
            更新时间: 'updateTime',
            修改时间: 'modifyTime',
            最后更新: 'lastUpdate',
            今天: 'today',
            昨天: 'yesterday',
            明天: 'tomorrow',
            本周: 'thisWeek',
            本月: 'thisMonth',
            本年: 'thisYear',

            // 基础类
            名称: 'name',
            标题: 'title',
            描述: 'description',
            备注: 'remark',
            说明: 'description',
            类型: 'type',
            状态: 'status',
            版本: 'version',
            编号: 'number',
            序号: 'index',
            标识: 'id',
            代码: 'code',
            值: 'value',
            内容: 'content',
            文本: 'text',
            链接: 'link',
            地址: 'address',
            路径: 'path',
            文件: 'file',
            文件夹: 'folder',
            目录: 'directory',

            // 提示类
            请选择: 'pleaseSelect',
            请输入: 'pleaseInput',
            请确认: 'pleaseConfirm',
            请等待: 'pleaseWait',
            暂无数据: 'noData',
            暂无: 'none',
            无: 'none',
            空: 'empty',
            全部: 'all',
            所有: 'all',
            任意: 'any',
            其他: 'other',
            更多: 'more',
            详细: 'detail',
            简单: 'simple',
            高级: 'advanced',
            基础: 'basic',

            // 单位类
            秒: 'second',
            分钟: 'minute',
            小时: 'hour',
            天: 'day',
            周: 'week',
            月: 'month',
            年: 'year',
            次: 'times',
            个: 'count',
            条: 'item',
            行: 'row',
            列: 'column',
            页: 'page',
            项: 'item',

            // 业务类
            产品: 'product',
            实例: 'instance',
            节点: 'node',
            核心: 'core',
            集群: 'cluster',
            服务: 'service',
            应用: 'application',
            功能: 'function',
            模块: 'module',
            组件: 'component',
            页面: 'page',
            管理: 'manage',
            系统: 'system',
            平台: 'platform',
            工具: 'tool',
            用户: 'user',
            角色: 'role',
            权限: 'permission',
            菜单: 'menu',
            按钮: 'button',
            表单: 'form',
            字段: 'field',
            规则: 'rule',
            策略: 'strategy',
            算法: 'algorithm',
            流程: 'process',
            步骤: 'step',
            阶段: 'stage',
            环节: 'link',
            环境: 'environment',
            部署: 'deploy',
            发布: 'release',
            版本: 'version',
            分支: 'branch',
            标签: 'tag',

            // 金融业务类
            证券: 'securities',
            股票: 'stock',
            债券: 'bond',
            基金: 'fund',
            期货: 'futures',
            期权: 'option',
            交易: 'trade',
            委托: 'order',
            成交: 'deal',
            行情: 'quote',
            价格: 'price',
            数量: 'quantity',
            金额: 'amount',
            余额: 'balance',
            资金: 'fund',
            账户: 'account',
            客户: 'customer',
            风控: 'riskControl',
            合规: 'compliance',
            审计: 'audit',
            报表: 'report',
            清算: 'settlement',
            结算: 'clearing'
        };
    }

    /**
     * 递归获取所有Vue和JS文件
     */
    getAllFiles(dir, files = []) {
        try {
            const items = fs.readdirSync(dir);

            for (const item of items) {
                const fullPath = path.join(dir, item);
                const stat = fs.statSync(fullPath);

                if (stat.isDirectory()) {
                    // 跳过特定目录，但包含之前生成的locales目录以外的所有目录
                    if (!['node_modules', 'dist', '.git', '.vscode'].includes(item) &&
                        !(item === 'locales' && fullPath.includes('src'))) {
                        this.getAllFiles(fullPath, files);
                    }
                } else if (stat.isFile()) {
                    // 收集所有Vue和JS文件，但排除之前生成的国际化文件
                    if (/\.(vue|js)$/.test(item) && !fullPath.includes('locales')) {
                        files.push(fullPath);
                    }
                }
            }
        } catch (error) {
            console.warn(`扫描目录 ${dir} 失败:`, error.message);
        }

        return files;
    }

    /**
     * 提取文件中的中文文案
     */
    extractFromFile(filePath) {
        try {
            const content = fs.readFileSync(filePath, 'utf-8');
            const texts = new Set();

            // 更精确的正则表达式模式
            const patterns = [
                // 双引号内的中文
                /"([^"]*[\u4e00-\u9fff][^"]*)"/g,
                // 单引号内的中文
                /'([^']*[\u4e00-\u9fff][^']*)'/g,
                // 模板字符串内的中文
                /`([^`]*[\u4e00-\u9fff][^`]*)`/g,
                // HTML标签内的中文文本
                />([^<>]*[\u4e00-\u9fff][^<>]*?)</g,
                // Vue指令中的中文
                /(?:title|placeholder|label|content|text|message)=["']([^"']*[\u4e00-\u9fff][^"']*)["']/g
            ];

            patterns.forEach(pattern => {
                let match;
                const globalRegex = new RegExp(pattern.source, pattern.flags);

                while ((match = globalRegex.exec(content)) !== null) {
                    const text = match[1];
                    if (text && this.isValidChineseText(text)) {
                        texts.add(text.trim());
                    }
                }
            });

            return Array.from(texts);
        } catch (error) {
            console.warn(`提取文件 ${filePath} 失败:`, error.message);
            return [];
        }
    }

    /**
     * 验证是否为有效的中文文案
     */
    isValidChineseText(text) {
        text = text.trim();

        // 基本过滤条件
        if (!text || text.length < 1 || text.length > 100) return false;
        if (!/[\u4e00-\u9fff]/.test(text)) return false;

        // 排除纯数字、符号、英文
        if (/^[\d\s\-_\.\/\\]+$/.test(text)) return false;
        if (/^[a-zA-Z\s\-_\.]+$/.test(text)) return false;

        // 排除代码片段和技术内容
        const excludePatterns = [
            /console\./,
            /import\s/,
            /export\s/,
            /function\s/,
            /^\$\w+/,
            /^@\w+/,
            /^#\w+/,
            /^\.\w+/,
            /^\/\//,
            /^\/\*/,
            /^\*\//,
            /^http/,
            /^www\./,
            /\.com$/,
            /\.(js|vue|css|html|json)$/,
            /^<\w+/,
            /^\w+>/,
            /^<!--/,
            /-->$/,
            /^\{\{/,
            /\}\}$/,
            /^v-\w+/,
            /^:\w+/,
            /^@\w+/,
            /^\$\w+/,
            /^ref\s*=/,
            /^key\s*=/,
            /^class\s*=/,
            /^style\s*=/,
            /^id\s*=/,
            /^data-\w+/,
            /^aria-\w+/,
            /^role\s*=/,
            /^\w+\s*\(\s*\)/,
            /^\w+\s*\{/,
            /\}\s*$/,
            /^\[\s*$/,
            /\]\s*$/,
            /^return\s/,
            /^if\s*\(/,
            /^for\s*\(/,
            /^while\s*\(/,
            /^switch\s*\(/,
            /^case\s/,
            /^default\s*:/,
            /^break\s*;/,
            /^continue\s*;/,
            /^var\s/,
            /^let\s/,
            /^const\s/,
            /^this\./,
            /process\.env/,
            /HUI_APP_NAME/,
            /^\w+\.\w+$/,
            /^[a-zA-Z_$][\w$]*$/,
            /^\/[\w\/\-\.]*$/,
            /^\d+$/,
            /^[\d\.\-\+\*\/\%\(\)\s]+$/,
            /^[A-Z_]+$/,
            /^[a-z]+[A-Z]/,
            /\$\{.*\}/,
            /\n|\r/,
            /^\s*$/
        ];

        return !excludePatterns.some(pattern => pattern.test(text));
    }

    /**
     * 生成有意义的国际化key
     */
    generateMeaningfulKey(text, category = '') {
        // 先尝试完全匹配
        if (this.mappings[text]) {
            return this.mappings[text];
        }

        // 分词处理
        const words = [];
        let remainingText = text;

        // 提取已知的中文词汇
        for (const [chinese, english] of Object.entries(this.mappings)) {
            if (remainingText.includes(chinese)) {
                words.push(english);
                remainingText = remainingText.replace(new RegExp(chinese, 'g'), ' ');
            }
        }

        // 处理剩余的中文字符
        const chineseChars = remainingText.match(/[\u4e00-\u9fff]/g);
        if (chineseChars && chineseChars.length > 0) {
            // 如果还有未识别的中文，根据长度生成描述性key
            if (chineseChars.length <= 2) {
                words.push('item');
            } else if (chineseChars.length <= 4) {
                words.push('label');
            } else if (chineseChars.length <= 8) {
                words.push('message');
            } else {
                words.push('description');
            }
        }

        // 如果没有提取到任何词汇，使用默认值
        if (words.length === 0) {
            words.push('text');
        }

        // 生成小驼峰格式的key
        let key = words
            .map((word, index) => {
                if (index === 0) {
                    return word.toLowerCase();
                }
                return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
            })
            .join('');

        // 避免重复key
        const baseKey = key;
        let counter = 1;
        const fullKey = category ? `${category}.${key}` : key;

        while (this.keyCounter.has(fullKey)) {
            key = `${baseKey}${counter}`;
            counter++;
            const newFullKey = category ? `${category}.${key}` : key;
            if (!this.keyCounter.has(newFullKey)) {
                break;
            }
        }

        this.keyCounter.set(category ? `${category}.${key}` : key, true);
        return key;
    }

    /**
     * 根据文件路径推断路由信息
     */
    inferRouteFromPath(filePath) {
        const relativePath = path.relative(this.srcDir, filePath);

        // 处理views目录下的文件 - 归类到modules
        if (relativePath.includes('views/index/') || relativePath.includes('views\\index\\')) {
            const fileName = path.basename(filePath, path.extname(filePath));
            return `modules.${fileName}`;
        }

        // 处理components目录下的文件 - 归类到modules
        if (relativePath.includes('components/') || relativePath.includes('components\\')) {
            const parts = relativePath.split(/[/\\]/);
            const componentIndex = parts.findIndex(part => part === 'components');
            if (componentIndex >= 0 && componentIndex + 1 < parts.length) {
                const componentName = parts[componentIndex + 1];
                return `modules.${componentName}`;
            }
        }

        // 处理其他目录
        if (relativePath.includes('api/') || relativePath.includes('api\\')) {
            return 'api';
        }

        if (relativePath.includes('utils/') || relativePath.includes('utils\\')) {
            return 'utils';
        }

        if (relativePath.includes('store/') || relativePath.includes('store\\')) {
            return 'store';
        }

        if (relativePath.includes('mixins/') || relativePath.includes('mixins\\')) {
            return 'mixins';
        }

        return 'common';
    }

    /**
     * 处理所有文件
     */
    processAllFiles() {
        console.log('🔍 扫描所有Vue和JS文件（排除locales目录）...');
        const allFiles = this.getAllFiles(this.srcDir);
        this.stats.totalFiles = allFiles.length;

        console.log(`📊 发现 ${allFiles.length} 个文件`);

        let processedCount = 0;

        allFiles.forEach(filePath => {
            processedCount++;
            const relativePath = path.relative(this.srcDir, filePath);

            if (processedCount % 50 === 0) {
                console.log(`📄 处理进度: ${processedCount}/${allFiles.length} (${Math.round(processedCount / allFiles.length * 100)}%)`);
            }

            const texts = this.extractFromFile(filePath);

            if (texts.length > 0) {
                const routeKey = this.inferRouteFromPath(filePath);

                if (!this.extractedTexts.has(routeKey)) {
                    this.extractedTexts.set(routeKey, []);
                }

                texts.forEach(text => {
                    if (!this.allTexts.has(text)) {
                        this.allTexts.add(text);
                        const key = this.generateMeaningfulKey(text, routeKey);
                        this.extractedTexts.get(routeKey).push({
                            text,
                            file: relativePath,
                            key
                        });
                    }
                });

                this.processedFiles.push({
                    file: relativePath,
                    textsCount: texts.length
                });
            }
        });

        this.stats.processedFiles = this.processedFiles.length;
        this.stats.totalTexts = this.allTexts.size;
    }

    /**
     * 生成国际化文件
     */
    generateI18nFiles() {
        // 确保输出目录存在
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }

        const zhCN = {};
        const enUS = {};
        const mapping = {};

        // 按路由组织数据
        this.extractedTexts.forEach((texts, routeKey) => {
            const [category, module] = routeKey.split('.');

            if (!zhCN[category]) zhCN[category] = {};
            if (!enUS[category]) enUS[category] = {};

            if (module) {
                if (!zhCN[category][module]) zhCN[category][module] = {};
                if (!enUS[category][module]) enUS[category][module] = {};
                if (!mapping[category]) mapping[category] = {};
                if (!mapping[category][module]) mapping[category][module] = [];

                texts.forEach(({ text, file, key }) => {
                    zhCN[category][module][key] = text;
                    enUS[category][module][key] = text; // 英文版本暂时使用中文

                    mapping[category][module].push({
                        key,
                        chinese: text,
                        english: text,
                        file
                    });
                });
            } else {
                if (!mapping[category]) mapping[category] = [];

                texts.forEach(({ text, file, key }) => {
                    zhCN[category][key] = text;
                    enUS[category][key] = text;

                    mapping[category].push({
                        key,
                        chinese: text,
                        english: text,
                        file
                    });
                });
            }
        });

        // 写入文件
        this.writeFile('zh-CN.js', `export default ${JSON.stringify(zhCN, null, 2)};`);
        this.writeFile('en-US.js', `export default ${JSON.stringify(enUS, null, 2)};`);
        this.writeFile('mapping.json', JSON.stringify(mapping, null, 2));

        // 生成索引文件
        const indexContent = `import zhCN from './zh-CN';
import enUS from './en-US';

export default {
    'zh-CN': zhCN,
    'en-US': enUS
};`;
        this.writeFile('index.js', indexContent);

        // 生成详细报告
        this.generateDetailedReport();
    }

    /**
     * 写入文件
     */
    writeFile(filename, content) {
        const filePath = path.join(this.outputDir, filename);
        fs.writeFileSync(filePath, content, 'utf-8');
        console.log(`✅ 生成文件: ${filePath}`);
    }

    /**
     * 生成详细报告
     */
    generateDetailedReport() {
        const endTime = Date.now();
        const duration = Math.round((endTime - this.stats.startTime) / 1000);

        const report = {
            summary: {
                totalFiles: this.stats.totalFiles,
                processedFiles: this.stats.processedFiles,
                totalTexts: this.stats.totalTexts,
                duration: `${duration}秒`,
                extractTime: new Date().toISOString()
            },
            categories: {},
            topFiles: this.processedFiles
                .sort((a, b) => b.textsCount - a.textsCount)
                .slice(0, 20)
        };

        // 统计各分类的文案数量
        this.extractedTexts.forEach((texts, routeKey) => {
            const [category, module] = routeKey.split('.');

            if (!report.categories[category]) {
                report.categories[category] = {
                    totalTexts: 0,
                    modules: {}
                };
            }

            report.categories[category].totalTexts += texts.length;

            if (module) {
                report.categories[category].modules[module] = texts.length;
            }
        });

        this.writeFile('extraction-report.json', JSON.stringify(report, null, 2));

        console.log('\n📊 优化提取统计:');
        console.log(`总文件数: ${report.summary.totalFiles}`);
        console.log(`处理文件数: ${report.summary.processedFiles}`);
        console.log(`总文案数: ${report.summary.totalTexts}`);
        console.log(`处理时间: ${report.summary.duration}`);

        console.log('\n🏆 文案最多的文件 (Top 10):');
        report.topFiles.slice(0, 10).forEach((file, index) => {
            console.log(`  ${index + 1}. ${file.file}: ${file.textsCount} 条`);
        });

        console.log('\n📁 分类统计:');
        Object.entries(report.categories).forEach(([category, data]) => {
            console.log(`  ${category}: ${data.totalTexts} 条文案`);
        });
    }

    /**
     * 执行优化提取
     */
    run() {
        console.log('🚀 开始优化的中文文案提取...');
        console.log(`源目录: ${this.srcDir}`);
        console.log(`输出目录: ${this.outputDir}`);

        this.processAllFiles();
        this.generateI18nFiles();

        console.log('✨ 优化提取完成!');
    }
}

// 执行脚本
if (require.main === module) {
    const extractor = new OptimizedI18nExtractor();
    extractor.run();
}

module.exports = OptimizedI18nExtractor;
